import * as assert from "assert"
import * as vscode from "vscode"
import { QaxNextEditUIProvider } from "../QaxNextEditUIProvider"
import { QaxChangeType, QaxJumpSuggestion } from "../types/QaxNextEditTypes"

// Jest globals
declare const jest: any
declare const expect: any
declare const describe: any
declare const it: any
declare const beforeEach: any
declare const afterEach: any

// Mock VS Code API
const mockStatusBarItem = {
	text: "",
	show: jest.fn(),
	hide: jest.fn(),
	dispose: jest.fn()
}

const mockDecorationType = {
	dispose: jest.fn()
}

const mockHoverProvider = {
	dispose: jest.fn()
}

const mockVscode = {
	window: {
		createStatusBarItem: () => mockStatusBarItem,
		createTextEditorDecorationType: () => mockDecorationType,
		showTextDocument: async (document: vscode.TextDocument) => ({
			document,
			setDecorations: jest.fn(),
			selection: new vscode.Selection(0, 0, 0, 0),
			revealRange: jest.fn()
		})
	},
	workspace: {
		onDidChangeTextDocument: () => ({ dispose: jest.fn() }),
		onDidOpenTextDocument: () => ({ dispose: jest.fn() }),
		onDidCloseTextDocument: () => ({ dispose: jest.fn() }),
		openTextDocument: async (uri: vscode.Uri) => ({
			uri,
			languageId: "typescript",
			getText: () => "mock content"
		})
	},
	languages: {
		registerHoverProvider: () => mockHoverProvider
	},
	StatusBarAlignment: {
		Right: 2
	},
	TextEditorRevealType: {
		InCenter: 1
	},
	ThemeColor: jest.fn(),
	MarkdownString: jest.fn().mockImplementation((value: any) => ({
		value,
		isTrusted: false,
		appendMarkdown: jest.fn(),
		appendCodeblock: jest.fn()
	})),
	Hover: jest.fn(),
	Uri: {
		file: (path: string) => ({ fsPath: path, scheme: "file" })
	},
	Range: vscode.Range,
	Position: vscode.Position,
	Selection: vscode.Selection,
	Location: vscode.Location
}

Object.assign(vscode, mockVscode)

describe("QaxNextEditUIProvider", () => {
	let uiProvider: QaxNextEditUIProvider

	beforeEach(() => {
		uiProvider = new QaxNextEditUIProvider()
		// Reset mocks
		jest.clearAllMocks()
	})

	afterEach(() => {
		uiProvider.dispose()
	})

	describe("Initialization", () => {
		it("should initialize correctly", () => {
			assert.ok(uiProvider)
		})

		it("should create status bar item", () => {
			// Status bar item should be created during initialization
			assert.ok(mockStatusBarItem)
		})

		it("should create decoration types", () => {
			// Decoration types should be created during initialization
			assert.ok(mockDecorationType)
		})
	})

	describe("Suggestion Display", () => {
		it("should handle empty suggestions", async () => {
			await uiProvider.showSuggestions([])
			
			// Status bar should be hidden for empty suggestions
			expect(mockStatusBarItem.hide).toHaveBeenCalled()
		})

		it("should display suggestions", async () => {
			const mockSuggestions: QaxJumpSuggestion[] = [
				{
					id: "test-1",
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 10),
					description: "Test suggestion",
					changeType: QaxChangeType.VARIABLE_RENAME,
					priority: 5,
					relatedChange: {
						type: QaxChangeType.VARIABLE_RENAME,
						filePath: "test.ts",
						range: new vscode.Range(0, 0, 0, 10),
						oldValue: "oldName",
						newValue: "newName",
						confidence: 0.9
					}
				}
			]

			await uiProvider.showSuggestions(mockSuggestions)
			
			// Status bar should show suggestion count
			expect(mockStatusBarItem.show).toHaveBeenCalled()
			expect(mockStatusBarItem.text).toContain("1/1")
		})

		it("should display multiple suggestions", async () => {
			const mockSuggestions: QaxJumpSuggestion[] = [
				{
					id: "test-1",
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 10),
					description: "Test suggestion 1",
					changeType: QaxChangeType.VARIABLE_RENAME,
					priority: 5,
					relatedChange: {
						type: QaxChangeType.VARIABLE_RENAME,
						filePath: "test.ts",
						range: new vscode.Range(0, 0, 0, 10),
						oldValue: "oldName",
						newValue: "newName",
						confidence: 0.9
					}
				},
				{
					id: "test-2",
					filePath: "other.ts",
					range: new vscode.Range(1, 0, 1, 10),
					description: "Test suggestion 2",
					changeType: QaxChangeType.FUNCTION_PARAMETER_CHANGE,
					priority: 3,
					relatedChange: {
						type: QaxChangeType.FUNCTION_PARAMETER_CHANGE,
						filePath: "other.ts",
						range: new vscode.Range(1, 0, 1, 10),
						oldValue: "function test(a)",
						newValue: "function test(a, b)",
						confidence: 0.8
					}
				}
			]

			await uiProvider.showSuggestions(mockSuggestions)
			
			// Status bar should show correct count
			expect(mockStatusBarItem.text).toContain("1/2")
		})
	})

	describe("Navigation", () => {
		beforeEach(async () => {
			const mockSuggestions: QaxJumpSuggestion[] = [
				{
					id: "test-1",
					filePath: "test1.ts",
					range: new vscode.Range(0, 0, 0, 10),
					description: "Test suggestion 1",
					changeType: QaxChangeType.VARIABLE_RENAME,
					priority: 5,
					relatedChange: {
						type: QaxChangeType.VARIABLE_RENAME,
						filePath: "test1.ts",
						range: new vscode.Range(0, 0, 0, 10),
						oldValue: "oldName",
						newValue: "newName",
						confidence: 0.9
					}
				},
				{
					id: "test-2",
					filePath: "test2.ts",
					range: new vscode.Range(1, 0, 1, 10),
					description: "Test suggestion 2",
					changeType: QaxChangeType.VARIABLE_RENAME,
					priority: 3,
					relatedChange: {
						type: QaxChangeType.VARIABLE_RENAME,
						filePath: "test2.ts",
						range: new vscode.Range(1, 0, 1, 10),
						oldValue: "oldName2",
						newValue: "newName2",
						confidence: 0.8
					}
				}
			]

			await uiProvider.showSuggestions(mockSuggestions)
		})

		it("should navigate to next suggestion", () => {
			// Initially at first suggestion (1/2)
			expect(mockStatusBarItem.text).toContain("1/2")

			uiProvider.navigateToNextSuggestion()
			
			// Should move to second suggestion (2/2)
			expect(mockStatusBarItem.text).toContain("2/2")
		})

		it("should wrap around when navigating past last suggestion", () => {
			// Navigate to last suggestion
			uiProvider.navigateToNextSuggestion()
			expect(mockStatusBarItem.text).toContain("2/2")

			// Navigate past last should wrap to first
			uiProvider.navigateToNextSuggestion()
			expect(mockStatusBarItem.text).toContain("1/2")
		})

		it("should navigate to previous suggestion", () => {
			// Navigate to second suggestion first
			uiProvider.navigateToNextSuggestion()
			expect(mockStatusBarItem.text).toContain("2/2")

			// Navigate back to first
			uiProvider.navigateToPreviousSuggestion()
			expect(mockStatusBarItem.text).toContain("1/2")
		})

		it("should wrap around when navigating before first suggestion", () => {
			// Initially at first suggestion
			expect(mockStatusBarItem.text).toContain("1/2")

			// Navigate before first should wrap to last
			uiProvider.navigateToPreviousSuggestion()
			expect(mockStatusBarItem.text).toContain("2/2")
		})

		it("should return current suggestion", () => {
			const current = uiProvider.getCurrentSuggestion()
			assert.ok(current)
			assert.strictEqual(current.id, "test-1")
		})

		it("should return null when no suggestions", async () => {
			await uiProvider.showSuggestions([])
			const current = uiProvider.getCurrentSuggestion()
			assert.strictEqual(current, null)
		})
	})

	describe("Event Handling", () => {
		it("should set event callback", () => {
			const mockCallback = jest.fn()
			uiProvider.setEventCallback(mockCallback)
			// No direct way to test this without triggering events
		})

		it("should handle navigation with no suggestions", () => {
			// Should not throw errors
			uiProvider.navigateToNextSuggestion()
			uiProvider.navigateToPreviousSuggestion()
		})
	})

	describe("UI Cleanup", () => {
		it("should clear UI", async () => {
			const mockSuggestions: QaxJumpSuggestion[] = [
				{
					id: "test-1",
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 10),
					description: "Test suggestion",
					changeType: QaxChangeType.VARIABLE_RENAME,
					priority: 5,
					relatedChange: {
						type: QaxChangeType.VARIABLE_RENAME,
						filePath: "test.ts",
						range: new vscode.Range(0, 0, 0, 10),
						oldValue: "oldName",
						newValue: "newName",
						confidence: 0.9
					}
				}
			]

			await uiProvider.showSuggestions(mockSuggestions)
			expect(mockStatusBarItem.show).toHaveBeenCalled()

			uiProvider.clearUI()
			expect(mockStatusBarItem.hide).toHaveBeenCalled()
		})

		it("should dispose cleanly", () => {
			uiProvider.dispose()
			
			// Should dispose all resources
			expect(mockStatusBarItem.dispose).toHaveBeenCalled()
			expect(mockDecorationType.dispose).toHaveBeenCalled()
		})

		it("should handle multiple dispose calls", () => {
			uiProvider.dispose()
			uiProvider.dispose() // Should not throw
		})
	})

	describe("Hover Provider", () => {
		it("should register hover provider when suggestions are shown", async () => {
			const mockSuggestions: QaxJumpSuggestion[] = [
				{
					id: "test-1",
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 10),
					description: "Test suggestion",
					changeType: QaxChangeType.VARIABLE_RENAME,
					priority: 5,
					relatedChange: {
						type: QaxChangeType.VARIABLE_RENAME,
						filePath: "test.ts",
						range: new vscode.Range(0, 0, 0, 10),
						oldValue: "oldName",
						newValue: "newName",
						confidence: 0.9
					}
				}
			]

			await uiProvider.showSuggestions(mockSuggestions)
			
			// Hover provider should be registered
			expect(mockVscode.languages.registerHoverProvider).toHaveBeenCalled()
		})

		it("should dispose hover provider on cleanup", async () => {
			const mockSuggestions: QaxJumpSuggestion[] = [
				{
					id: "test-1",
					filePath: "test.ts",
					range: new vscode.Range(0, 0, 0, 10),
					description: "Test suggestion",
					changeType: QaxChangeType.VARIABLE_RENAME,
					priority: 5,
					relatedChange: {
						type: QaxChangeType.VARIABLE_RENAME,
						filePath: "test.ts",
						range: new vscode.Range(0, 0, 0, 10),
						oldValue: "oldName",
						newValue: "newName",
						confidence: 0.9
					}
				}
			]

			await uiProvider.showSuggestions(mockSuggestions)
			uiProvider.clearUI()
			
			// Hover provider should be disposed
			expect(mockHoverProvider.dispose).toHaveBeenCalled()
		})
	})
})
