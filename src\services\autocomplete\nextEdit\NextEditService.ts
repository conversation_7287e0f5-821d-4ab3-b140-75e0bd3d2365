import * as vscode from "vscode"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../api"
import { AutocompleteTaskManager } from "../AutocompleteTaskManager"
import { NextEditContextCollector } from "./NextEditContextCollector"
import { NextEditRecommendationEngine } from "./NextEditRecommendationEngine"
import { NextEditUIProvider } from "./NextEditUIProvider"
import {
	NextEditConfig,
	NextEditFileState,
	NextEditSuggestion,
	NextEditEvent,
	NextEditEventType,
	NextEditType,
	DEFAULT_NEXT_EDIT_CONFIG,
} from "./types/NextEditTypes"
import { createDebouncedFn } from "../utils/createDebouncedFn"
import { QaxNextEditService } from "../qaxNextEdit/QaxNextEditService"
import { QaxJumpSuggestion } from "../qaxNextEdit/types/QaxNextEditTypes"

/**
 * Main service for Next Edit functionality
 */
export class NextEditService {
	private static instance: NextEditService | null = null

	private config: NextEditConfig
	private contextCollector: NextEditContextCollector
	private recommendationEngine: NextEditRecommendationEngine
	private uiProvider: NextEditUIProvider
	private fileStates: Map<string, NextEditFileState> = new Map()
	private disposables: vscode.Disposable[] = []
	private isEnabled: boolean = true
	private debouncedRequestSuggestions: (filePath: string, document: vscode.TextDocument) => void

	private constructor(config: NextEditConfig = DEFAULT_NEXT_EDIT_CONFIG, apiHandler: ApiHandler | null = null) {
		this.config = config
		this.contextCollector = new NextEditContextCollector(config)
		this.recommendationEngine = new NextEditRecommendationEngine(apiHandler || undefined)
		this.uiProvider = new NextEditUIProvider()

		// Create debounced function for requesting suggestions
		this.debouncedRequestSuggestions = createDebouncedFn(this.requestSuggestions.bind(this), config.debounceDelayMs)

		// Initialize QaxNextEdit service if needed
		this.initializeQaxNextEditIfNeeded()

		this.setupEventHandlers()
		this.setupUIEventHandlers()
	}

	/**
	 * Get singleton instance
	 */
	static getInstance(config?: NextEditConfig, apiHandler?: ApiHandler | null): NextEditService {
		if (!NextEditService.instance) {
			NextEditService.instance = new NextEditService(config, apiHandler)
		}
		return NextEditService.instance
	}

	/**
	 * Dispose singleton instance
	 */
	static dispose(): void {
		if (NextEditService.instance) {
			NextEditService.instance.dispose()
			NextEditService.instance = null
		}
	}

	/**
	 * Initialize the service
	 */
	initialize(apiHandler: ApiHandler | null): void {
		this.recommendationEngine.updateApiHandler(apiHandler)
	}

	/**
	 * Initialize QaxNextEdit service if needed
	 */
	private initializeQaxNextEditIfNeeded(): void {
		try {
			const config = vscode.workspace.getConfiguration("qax-code.nextEdit")
			const useQaxNextEdit = config.get<boolean>("useQaxNextEdit", false)

			if (useQaxNextEdit) {
				console.log("🔍 NextEdit: Initializing QaxNextEdit service...")
				// Get QaxNextEdit service instance to ensure it's initialized
				const qaxService = QaxNextEditService.getInstance()
				// Verify service is working
				const state = qaxService.getState()
				console.log("🔍 NextEdit: QaxNextEdit service initialized successfully, enabled:", state.isEnabled)
			}
		} catch (error) {
			console.error("NextEdit: Failed to initialize QaxNextEdit service:", error)
		}
	}

	/**
	 * Setup event handlers for file changes and editor events
	 */
	private setupEventHandlers(): void {
		// Listen for document changes
		this.disposables.push(
			vscode.workspace.onDidChangeTextDocument((event) => {
				if (this.shouldProcessDocument(event.document)) {
					this.onDocumentChanged(event.document)
				}
			}),
		)

		// Listen for active editor changes
		this.disposables.push(
			vscode.window.onDidChangeActiveTextEditor((editor) => {
				if (editor && this.shouldProcessDocument(editor.document)) {
					this.onActiveEditorChanged(editor)
				}
			}),
		)

		// Listen for document open/close
		this.disposables.push(
			vscode.workspace.onDidOpenTextDocument((document) => {
				if (this.shouldProcessDocument(document)) {
					this.onDocumentOpened(document)
				}
			}),
		)

		this.disposables.push(
			vscode.workspace.onDidCloseTextDocument((document) => {
				// Note: We don't remove suggestions when document is closed
				// as per requirement to keep suggestions until file is modified
			}),
		)
	}

	/**
	 * Setup UI event handlers
	 */
	private setupUIEventHandlers(): void {
		this.uiProvider.setEventCallback((event: NextEditEvent) => {
			this.handleUIEvent(event)
		})

		// Commands are now registered in NextEditProvider to avoid duplication
	}

	/**
	 * Check if document should be processed
	 */
	private shouldProcessDocument(document: vscode.TextDocument): boolean {
		// Skip if service is disabled
		if (!this.isEnabled || !this.config.enabled) {
			return false
		}

		// Skip if autocomplete is temporarily disabled
		const taskManager = AutocompleteTaskManager.getInstance()
		if (taskManager.isTemporarilyDisabled()) {
			return false
		}

		// Skip certain file types
		const scheme = document.uri.scheme
		if (scheme !== "file" && scheme !== "untitled") {
			return false
		}

		// Skip very small files
		if (document.lineCount <= 2) {
			return false
		}

		return true
	}

	/**
	 * Handle document changes
	 */
	private onDocumentChanged(document: vscode.TextDocument): void {
		const filePath = document.uri.fsPath

		// Clear existing suggestions for this file
		this.clearSuggestionsForFile(filePath)

		// Request new suggestions (debounced)
		this.debouncedRequestSuggestions(filePath, document)
	}

	/**
	 * Handle active editor changes
	 */
	private onActiveEditorChanged(editor: vscode.TextEditor): void {
		const filePath = editor.document.uri.fsPath
		const fileState = this.fileStates.get(filePath)

		if (fileState && fileState.suggestions.length > 0) {
			// Show existing suggestions for this file
			this.showSuggestionsForFile(filePath)
		}
	}

	/**
	 * Handle document opened
	 */
	private onDocumentOpened(document: vscode.TextDocument): void {
		const filePath = document.uri.fsPath
		const fileState = this.fileStates.get(filePath)

		if (fileState && fileState.suggestions.length > 0) {
			// Show existing suggestions for this file
			this.showSuggestionsForFile(filePath)
		}
	}

	/**
	 * Request suggestions for a file
	 */
	private async requestSuggestions(filePath: string, document: vscode.TextDocument): Promise<void> {
		try {
			// Check if QaxNextEdit should be used
			const config = vscode.workspace.getConfiguration("qax-code.nextEdit")
			const useQaxNextEdit = config.get<boolean>("useQaxNextEdit", true)

			if (useQaxNextEdit) {
				console.log("🚀🔍 QaxNextEdit: Using QaxNextEdit service for", filePath)
				await this.requestSuggestionsFromQaxNextEdit(filePath, document)
			} else {
				console.log("🚀🔍 NextEdit: Requesting suggestions for", filePath)
				await this.requestSuggestionsFromOriginal(filePath, document)
			}
		} catch (error) {
			console.error("NextEditService: Failed to request suggestions:", error)
		}
	}

	/**
	 * Request suggestions using QaxNextEdit service
	 */
	private async requestSuggestionsFromQaxNextEdit(filePath: string, document: vscode.TextDocument): Promise<void> {
		try {
			console.log("🔍 QaxNextEdit: Requesting suggestions for", filePath)

			// Get QaxNextEdit service instance
			const qaxService = QaxNextEditService.getInstance()
			console.log("🔍 QaxNextEdit: Service instance obtained, state:", qaxService.getState())

			// Trigger analysis for this document if needed
			console.log("🔍 QaxNextEdit: Triggering analysis for document changes")

			// Check if QaxNextEdit has any suggestions for this file
			const jumpSuggestions = qaxService.getJumpSuggestions(filePath)

			console.log(`📋 QaxNextEdit: Found ${jumpSuggestions.length} jump suggestions for ${filePath}`)

			// If no suggestions, check if we need to wait for analysis
			if (jumpSuggestions.length === 0) {
				console.log("🔍 QaxNextEdit: No suggestions found, checking if analysis is in progress...")
				const state = qaxService.getState()
				console.log("🔍 QaxNextEdit: Service state:", {
					isEnabled: state.isEnabled,
					isAnalyzing: state.isAnalyzing,
					pendingChanges: state.pendingChanges.size,
					cachedResults: state.cachedResults.size
				})

				// If analysis is in progress, wait a bit and try again
				if (state.isAnalyzing || state.pendingChanges.has(filePath)) {
					console.log("🔍 QaxNextEdit: Analysis in progress, waiting...")
					await new Promise(resolve => setTimeout(resolve, 500))
					const retryJumpSuggestions = qaxService.getJumpSuggestions(filePath)
					console.log(`📋 QaxNextEdit: After waiting, found ${retryJumpSuggestions.length} jump suggestions`)
					if (retryJumpSuggestions.length > 0) {
						const suggestions = this.convertQaxSuggestionsToNextEditSuggestions(retryJumpSuggestions)
						const limitedSuggestions = suggestions.slice(0, this.config.maxSuggestions)

						// Store suggestions
						this.fileStates.set(filePath, {
							filePath,
							suggestions: limitedSuggestions,
							currentSuggestionIndex: 0,
							isShowingSuggestions: false,
							lastModified: new Date(),
						})

						// Show suggestions if this is the active file
						const activeEditor = vscode.window.activeTextEditor
						if (activeEditor && activeEditor.document.uri.fsPath === filePath) {
							this.showSuggestionsForFile(filePath)
						}

						console.log(`🚀✨ QaxNextEdit: Stored ${limitedSuggestions.length} suggestions after retry`)
						return
					}
				}
			}

			if (jumpSuggestions.length > 0) {
				// Convert QaxJumpSuggestions to NextEditSuggestions
				const suggestions = this.convertQaxSuggestionsToNextEditSuggestions(jumpSuggestions)

				console.log(`🔄 QaxNextEdit: Converted to ${suggestions.length} NextEdit suggestions`)

				// Limit to max suggestions
				const limitedSuggestions = suggestions.slice(0, this.config.maxSuggestions)

				// Store suggestions
				this.fileStates.set(filePath, {
					filePath,
					suggestions: limitedSuggestions,
					currentSuggestionIndex: 0,
					isShowingSuggestions: false,
					lastModified: new Date(),
				})

				// Show suggestions if this is the active file
				const activeEditor = vscode.window.activeTextEditor
				if (activeEditor && activeEditor.document.uri.fsPath === filePath) {
					this.showSuggestionsForFile(filePath)
				}

				console.log(`🚀✨ QaxNextEdit: Stored ${limitedSuggestions.length} suggestions for ${filePath}`)

				// Log the suggestions for debugging
				limitedSuggestions.forEach((suggestion, index) => {
					console.log(`  ${index + 1}. ${suggestion.type}: ${suggestion.description}`)
					console.log(`     Location: ${suggestion.location.anchor} (${suggestion.location.position})`)
					console.log(`     Patch: "${suggestion.patch.oldContent}" → "${suggestion.patch.newContent}"`)
				})
			} else {
				console.log("📭 QaxNextEdit: No suggestions available for", filePath)
			}
		} catch (error) {
			console.error("NextEditService: Failed to request suggestions from QaxNextEdit:", error)
		}
	}

	/**
	 * Request suggestions using original method
	 */
	private async requestSuggestionsFromOriginal(filePath: string, document: vscode.TextDocument): Promise<void> {
		try {
			// Get current cursor position from active editor
			const editor = vscode.window.activeTextEditor
			const position = editor && editor.document.uri.fsPath === filePath ? editor.selection.active : undefined

			// Collect context with cursor position
			const context = await this.contextCollector.collectContext(document, position)

			// Generate suggestions
			const suggestions = await this.recommendationEngine.generateSuggestions(context)

			if (suggestions.length > 0) {
				// Limit to max suggestions
				const limitedSuggestions = suggestions.slice(0, this.config.maxSuggestions)

				// Store suggestions
				this.fileStates.set(filePath, {
					filePath,
					suggestions: limitedSuggestions,
					currentSuggestionIndex: 0,
					isShowingSuggestions: false,
					lastModified: new Date(),
				})

				// Show suggestions if this is the active file
				const activeEditor = vscode.window.activeTextEditor
				if (activeEditor && activeEditor.document.uri.fsPath === filePath) {
					this.showSuggestionsForFile(filePath)
				}

				console.log(`🚀✨ NextEdit: Stored ${limitedSuggestions.length} suggestions for ${filePath}`)
			}
		} catch (error) {
			console.error("NextEditService: Failed to request suggestions from original method:", error)
		}
	}

	/**
	 * Convert QaxJumpSuggestions to NextEditSuggestions format
	 */
	private convertQaxSuggestionsToNextEditSuggestions(qaxSuggestions: QaxJumpSuggestion[]): NextEditSuggestion[] {
		return qaxSuggestions.map((qaxSuggestion, index) => {
			// Determine suggestion type based on change type
			let type: NextEditType = NextEditType.MODIFY
			let description = qaxSuggestion.description

			if (qaxSuggestion.relatedChange) {
				const change = qaxSuggestion.relatedChange
				if (change.type === "variable_rename" || change.type === "function_parameter_change") {
					type = NextEditType.MODIFY
					description = `Change: ${change.oldValue} ➜ ${change.newValue || "updated"}`
				} else if (change.type === "function_call_deletion" || change.type === "variable_deletion") {
					type = NextEditType.DELETE
					description = `Remove: ${change.oldValue}`
				}
			}

			// Create anchor from the range and file content
			const anchor = this.createAnchorFromRange(qaxSuggestion.filePath, qaxSuggestion.range)

			// Determine old and new content
			let oldContent = ""
			let newContent = ""

			if (qaxSuggestion.suggestedEdit) {
				newContent = qaxSuggestion.suggestedEdit.newText
				// For modify/delete operations, we need the old content
				if (type === NextEditType.MODIFY || type === NextEditType.DELETE) {
					oldContent = this.getContentAtRange(qaxSuggestion.filePath, qaxSuggestion.range)
				}
			}

			return {
				id: qaxSuggestion.id || `qax_suggestion_${index}`,
				type,
				description,
				location: {
					anchor,
					position: type === NextEditType.DELETE ? "replace" : "replace"
				},
				patch: {
					oldContent,
					newContent
				},
				reasoning: `QaxNextEdit detected ${qaxSuggestion.changeType} with ${Math.round((qaxSuggestion.relatedChange?.confidence || 0.8) * 100)}% confidence`,
				filePath: qaxSuggestion.filePath,
				createdAt: new Date()
			}
		})
	}

	/**
	 * Create an anchor string from file path and range
	 */
	private createAnchorFromRange(filePath: string, range: vscode.Range): string {
		try {
			// Try to get the document content
			const content = this.getContentAtRange(filePath, range)
			if (content) {
				// Use the actual content as anchor, truncated if too long
				return content.length > 50 ? content.substring(0, 47) + "..." : content
			}
		} catch (error) {
			// Fallback to line/character position
		}

		// Fallback to position-based anchor
		return `line_${range.start.line + 1}_char_${range.start.character}`
	}

	/**
	 * Get content at a specific range in a file
	 */
	private getContentAtRange(filePath: string, range: vscode.Range): string {
		try {
			// Try to find the document in open editors
			const openDoc = vscode.workspace.textDocuments.find(doc => doc.uri.fsPath === filePath)
			if (openDoc) {
				return openDoc.getText(range)
			}

			// If not found in open documents, return empty string
			// In a real implementation, we might want to read from file system
			return ""
		} catch (error) {
			return ""
		}
	}

	/**
	 * Show suggestions for a file
	 */
	private async showSuggestionsForFile(filePath: string): Promise<void> {
		const fileState = this.fileStates.get(filePath)
		if (!fileState || fileState.suggestions.length === 0) {
			return
		}

		fileState.isShowingSuggestions = true

		// Use the new showSuggestions method that handles multiple suggestions
		await this.uiProvider.showSuggestions(fileState.suggestions)
	}

	/**
	 * Clear suggestions for a file
	 */
	private clearSuggestionsForFile(filePath: string): void {
		const fileState = this.fileStates.get(filePath)
		if (fileState) {
			fileState.suggestions = []
			fileState.currentSuggestionIndex = 0
			fileState.isShowingSuggestions = false
		}

		this.uiProvider.clearUI()
	}

	/**
	 * Navigate to next suggestion
	 */
	public async navigateToNextSuggestion(): Promise<void> {
		const activeEditor = vscode.window.activeTextEditor
		if (!activeEditor) {
			return
		}

		const filePath = activeEditor.document.uri.fsPath
		const fileState = this.fileStates.get(filePath)
		if (!fileState || fileState.suggestions.length === 0) {
			return
		}

		fileState.currentSuggestionIndex = (fileState.currentSuggestionIndex + 1) % fileState.suggestions.length
		await this.showSuggestionsForFile(filePath)
	}

	/**
	 * Navigate to previous suggestion
	 */
	public async navigateToPreviousSuggestion(): Promise<void> {
		const activeEditor = vscode.window.activeTextEditor
		if (!activeEditor) {
			return
		}

		const filePath = activeEditor.document.uri.fsPath
		const fileState = this.fileStates.get(filePath)
		if (!fileState || fileState.suggestions.length === 0) {
			return
		}

		fileState.currentSuggestionIndex =
			fileState.currentSuggestionIndex === 0 ? fileState.suggestions.length - 1 : fileState.currentSuggestionIndex - 1
		await this.showSuggestionsForFile(filePath)
	}

	/**
	 * Handle UI events
	 */
	private handleUIEvent(event: NextEditEvent): void {
		if (event.type === NextEditEventType.SUGGESTION_APPLIED || event.type === NextEditEventType.SUGGESTION_IGNORED) {
			// Move to next suggestion or clear if no more
			const filePath = event.filePath
			if (filePath) {
				const fileState = this.fileStates.get(filePath)
				if (fileState) {
					// Remove the current suggestion
					fileState.suggestions.splice(fileState.currentSuggestionIndex, 1)

					if (fileState.suggestions.length === 0) {
						// No more suggestions
						this.clearSuggestionsForFile(filePath)
					} else {
						// Adjust index if needed
						if (fileState.currentSuggestionIndex >= fileState.suggestions.length) {
							fileState.currentSuggestionIndex = 0
						}
						// Show next suggestion
						this.showSuggestionsForFile(filePath)
					}
				}
			}
		}
	}

	/**
	 * Enable/disable the service
	 */
	setEnabled(enabled: boolean): void {
		this.isEnabled = enabled
		if (!enabled) {
			// Clear all UI when disabled
			this.uiProvider.clearUI()
		}
		console.log(`🚀🔍 NextEditService ${enabled ? "enabled" : "disabled"}`)
	}

	/**
	 * Update configuration
	 */
	updateConfig(config: Partial<NextEditConfig>): void {
		this.config = { ...this.config, ...config }
		this.contextCollector.updateConfig(this.config)

		// Recreate debounced function if delay changed
		if (config.debounceDelayMs) {
			this.debouncedRequestSuggestions = createDebouncedFn(this.requestSuggestions.bind(this), config.debounceDelayMs)
		}
	}

	/**
	 * Update API handler
	 */
	updateApiHandler(apiHandler: ApiHandler | null): void {
		this.recommendationEngine.updateApiHandler(apiHandler)
	}

	/**
	 * Dispose the service
	 */
	dispose(): void {
		this.disposables.forEach((disposable) => disposable.dispose())
		this.disposables = []
		this.uiProvider.dispose()
		this.fileStates.clear()
		NextEditService.instance = null
		console.log("🚀🔍 NextEditService disposed")
	}
}
