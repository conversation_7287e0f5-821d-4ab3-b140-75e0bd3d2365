import * as assert from "assert"
import * as vscode from "vscode"
import { QaxChangeDetector } from "../services/QaxChangeDetector"
import { QaxChangeType, DEFAULT_QAX_NEXT_EDIT_CONFIG, QaxAnalysisContext } from "../types/QaxNextEditTypes"

// Mock services
const mockLSPService = {
	isLSPAvailable: async () => true,
	getDocumentSymbols: async () => [] as any[],
	getReferences: async () => [] as vscode.Location[],
	getDefinitions: async () => [] as vscode.Location[],
	detectSymbolRename: async () => null
}

const mockASTService = {
	parseDocument: async () => null as any,
	detectVariableRename: () => [] as any[],
	detectFunctionParameterChanges: () => [] as any[],
	detectFunctionCallDeletions: () => [] as any[]
}

const mockJumpSuggestionEngine = {
	generateJumpSuggestions: async () => []
}

// Mock services will be set up in beforeEach

// Mock VS Code API
const mockVscode = {
	workspace: {
		openTextDocument: async (options: any) => ({
			uri: { fsPath: "mock.ts" },
			languageId: options.language || "typescript",
			getText: () => options.content || "mock content",
			offsetAt: (position: vscode.Position) => position.line * 100 + position.character
		})
	},
	Position: vscode.Position,
	Range: vscode.Range
}

Object.assign(vscode, mockVscode)

describe("QaxChangeDetector", () => {
	let detector: QaxChangeDetector

	beforeEach(() => {
		detector = new QaxChangeDetector(DEFAULT_QAX_NEXT_EDIT_CONFIG)
	})

	describe("Initialization", () => {
		it("should initialize with config", () => {
			const customConfig = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				confidenceThreshold: 0.8
			}
			const customDetector = new QaxChangeDetector(customConfig)
			assert.ok(customDetector)
		})

		it("should update config", () => {
			const newConfig = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				confidenceThreshold: 0.9
			}
			detector.updateConfig(newConfig)
			// Config should be updated (no direct way to test this without exposing internal state)
		})
	})

	describe("Change Analysis", () => {
		it("should analyze changes for supported language", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" },
				languageId: "typescript",
				getText: () => "let x = 5;",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: mockDocument,
				changes: [
					{
						range: new vscode.Range(0, 4, 0, 5),
						rangeLength: 1,
						rangeOffset: 4,
						text: "y"
					}
				],
				beforeContent: "let x = 5;",
				afterContent: "let y = 5;",
				languageId: "typescript"
			}

			const result = await detector.analyzeChanges(context)
			assert.ok(result)
			assert.ok(typeof result.analysisTime === "number")
			assert.ok(typeof result.confidence === "number")
			assert.ok(Array.isArray(result.detectedChanges))
			assert.ok(Array.isArray(result.jumpSuggestions))
			assert.ok(result.metadata)
		})

		it("should return empty result for unsupported language", async () => {
			const mockDocument = {
				uri: { fsPath: "test.txt" },
				languageId: "plaintext",
				getText: () => "plain text",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.txt",
				document: mockDocument,
				changes: [],
				beforeContent: "plain text",
				afterContent: "plain text",
				languageId: "plaintext"
			}

			const result = await detector.analyzeChanges(context)
			assert.strictEqual(result.detectedChanges.length, 0)
			assert.strictEqual(result.jumpSuggestions.length, 0)
			assert.strictEqual(result.confidence, 0)
			assert.ok(result.metadata?.unsupportedLanguage)
		})

		it("should handle analysis errors gracefully", async () => {
			// Mock LSP service to throw error
			mockLSPService.isLSPAvailable = async () => {
				throw new Error("LSP error")
			}

			const mockDocument = {
				uri: { fsPath: "test.ts" },
				languageId: "typescript",
				getText: () => "let x = 5;",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: mockDocument,
				changes: [],
				beforeContent: "let x = 5;",
				afterContent: "let x = 5;",
				languageId: "typescript"
			}

			const result = await detector.analyzeChanges(context)
			assert.strictEqual(result.detectedChanges.length, 0)
			assert.strictEqual(result.jumpSuggestions.length, 0)
			assert.strictEqual(result.confidence, 0)
			assert.ok(result.metadata?.error)

			// Restore mock
			mockLSPService.isLSPAvailable = async () => true
		})
	})

	describe("Text Change Detection", () => {
		it("should detect variable rename pattern", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" },
				languageId: "typescript",
				getText: () => "let newName = 5;",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: mockDocument,
				changes: [
					{
						range: new vscode.Range(0, 4, 0, 11),
						rangeLength: 7, // "oldName".length
						rangeOffset: 4,
						text: "newName"
					}
				],
				beforeContent: "let oldName = 5;",
				afterContent: "let newName = 5;",
				languageId: "typescript"
			}

			const result = await detector.analyzeChanges(context)
			// Should detect at least one change (text-based detection)
			assert.ok(result.detectedChanges.length >= 0) // May be filtered out by confidence threshold
		})

		it("should detect function call deletion pattern", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" },
				languageId: "typescript",
				getText: () => "",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: mockDocument,
				changes: [
					{
						range: new vscode.Range(0, 0, 0, 7),
						rangeLength: 7, // "test();".length
						rangeOffset: 0,
						text: ""
					}
				],
				beforeContent: "test();",
				afterContent: "",
				languageId: "typescript"
			}

			const result = await detector.analyzeChanges(context)
			// Should detect deletion pattern
			assert.ok(result.detectedChanges.length >= 0)
		})
	})

	describe("LSP Integration", () => {
		it("should use LSP when available and enabled", async () => {
			const config = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				enableLSPIntegration: true
			}
			const lspDetector = new QaxChangeDetector(config)

			mockLSPService.isLSPAvailable = async () => true
			mockLSPService.getDocumentSymbols = async () => [
				{
					name: "testSymbol",
					kind: vscode.SymbolKind.Variable,
					location: new vscode.Location(vscode.Uri.file("test.ts"), new vscode.Range(0, 0, 0, 10)),
					containerName: undefined,
					detail: undefined
				}
			] as any[]

			const mockDocument = {
				uri: { fsPath: "test.ts" },
				languageId: "typescript",
				getText: () => "let testSymbol = 5;",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: mockDocument,
				changes: [],
				beforeContent: "let testSymbol = 5;",
				afterContent: "let testSymbol = 5;",
				languageId: "typescript"
			}

			const result = await lspDetector.analyzeChanges(context)
			assert.ok(result.metadata?.lspAvailable)
			assert.ok(context.symbols) // Should populate symbols from LSP
		})

		it("should skip LSP when disabled", async () => {
			const config = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				enableLSPIntegration: false
			}
			const noLspDetector = new QaxChangeDetector(config)

			const mockDocument = {
				uri: { fsPath: "test.ts" },
				languageId: "typescript",
				getText: () => "let x = 5;",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: mockDocument,
				changes: [],
				beforeContent: "let x = 5;",
				afterContent: "let x = 5;",
				languageId: "typescript"
			}

			const result = await noLspDetector.analyzeChanges(context)
			assert.strictEqual(result.metadata?.lspAvailable, false)
		})
	})

	describe("AST Analysis", () => {
		it("should use AST when enabled", async () => {
			const config = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				enableASTAnalysis: true
			}
			const astDetector = new QaxChangeDetector(config)

			// Mock AST service to return some changes
			mockASTService.parseDocument = async () => ({
				type: "program",
				text: "let x = 5;",
				range: new vscode.Range(0, 0, 0, 10),
				children: []
			}) as any
			mockASTService.detectVariableRename = () => [
				{
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: new vscode.Range(0, 4, 0, 5),
					oldValue: "x",
					newValue: "y",
					confidence: 0.9
				}
			] as any[]

			const mockDocument = {
				uri: { fsPath: "test.ts" },
				languageId: "typescript",
				getText: () => "let y = 5;",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: mockDocument,
				changes: [],
				beforeContent: "let x = 5;",
				afterContent: "let y = 5;",
				languageId: "typescript"
			}

			const result = await astDetector.analyzeChanges(context)
			assert.ok(result.metadata?.astParsed)
			assert.ok(result.detectedChanges.length > 0)

			// Restore mocks
			mockASTService.parseDocument = async () => null
			mockASTService.detectVariableRename = () => []
		})

		it("should skip AST when disabled", async () => {
			const config = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				enableASTAnalysis: false
			}
			const noAstDetector = new QaxChangeDetector(config)

			const mockDocument = {
				uri: { fsPath: "test.ts" },
				languageId: "typescript",
				getText: () => "let x = 5;",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: mockDocument,
				changes: [],
				beforeContent: "let x = 5;",
				afterContent: "let x = 5;",
				languageId: "typescript"
			}

			const result = await noAstDetector.analyzeChanges(context)
			assert.strictEqual(result.metadata?.astParsed, false)
		})
	})

	describe("Confidence Filtering", () => {
		it("should filter out low confidence changes", async () => {
			const config = {
				...DEFAULT_QAX_NEXT_EDIT_CONFIG,
				confidenceThreshold: 0.8
			}
			const strictDetector = new QaxChangeDetector(config)

			// Mock to return low confidence change
			mockASTService.parseDocument = async () => ({
				type: "program",
				text: "let x = 5;",
				range: new vscode.Range(0, 0, 0, 10),
				children: []
			}) as any
			mockASTService.detectVariableRename = () => [
				{
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: "test.ts",
					range: new vscode.Range(0, 4, 0, 5),
					oldValue: "x",
					newValue: "y",
					confidence: 0.5 // Below threshold
				}
			] as any[]

			const mockDocument = {
				uri: { fsPath: "test.ts" },
				languageId: "typescript",
				getText: () => "let y = 5;",
				offsetAt: (position: vscode.Position) => position.line * 100 + position.character
			} as vscode.TextDocument

			const context: QaxAnalysisContext = {
				filePath: "test.ts",
				document: mockDocument,
				changes: [],
				beforeContent: "let x = 5;",
				afterContent: "let y = 5;",
				languageId: "typescript"
			}

			const result = await strictDetector.analyzeChanges(context)
			// Low confidence change should be filtered out
			assert.strictEqual(result.detectedChanges.length, 0)

			// Restore mocks
			mockASTService.parseDocument = async () => null
			mockASTService.detectVariableRename = () => []
		})
	})
})
