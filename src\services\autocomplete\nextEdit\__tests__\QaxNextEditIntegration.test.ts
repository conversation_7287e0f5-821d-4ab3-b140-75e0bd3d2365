/**
 * Integration test for QaxNextEdit with NextEdit service
 * Tests the complete workflow from QaxNextEdit suggestions to NextEdit format
 */

import * as assert from "assert"
import * as vscodeTypes from "vscode"

// Mock VS Code API
const mockVscode = {
	workspace: {
		onDidChangeTextDocument: () => ({ dispose: () => {} }),
		onDidOpenTextDocument: () => ({ dispose: () => {} }),
		onDidCloseTextDocument: () => ({ dispose: () => {} }),
		onDidChangeConfiguration: () => ({ dispose: () => {} }),
		getConfiguration: (section?: string) => ({
			get: (key: string, defaultValue?: any) => {
				if (section === "qax-code.nextEdit" && key === "useQaxNextEdit") {
					return true // Enable QaxNextEdit for testing
				}
				return defaultValue
			},
			update: () => Promise.resolve()
		}),
		openTextDocument: async (options: any) => ({
			uri: { fsPath: options.content ? "test.ts" : "test.ts" },
			languageId: options.language || "typescript",
			getText: (range?: any) => {
				const content = options.content || "let oldName = 5;\nconsole.log(oldName);"
				if (!range) return content
				const lines = content.split('\n')
				return lines[range.start.line]?.substring(range.start.character, range.end.character) || ""
			},
			offsetAt: (position: any) => position.line * 100 + position.character
		}),
		textDocuments: []
	},
	window: {
		onDidChangeActiveTextEditor: () => ({ dispose: () => {} }),
		createStatusBarItem: () => ({
			text: "",
			show: () => {},
			hide: () => {},
			dispose: () => {}
		}),
		createTextEditorDecorationType: () => ({
			dispose: () => {}
		}),
		showTextDocument: async (document: any) => ({
			document,
			setDecorations: () => {},
			selection: { start: { line: 0, character: 0 }, end: { line: 0, character: 0 } },
			revealRange: () => {}
		}),
		activeTextEditor: null
	},
	languages: {
		onDidChangeDiagnostics: () => ({ dispose: () => {} }),
		registerHoverProvider: () => ({ dispose: () => {} })
	},
	commands: {
		executeCommand: async () => null,
		registerCommand: () => ({ dispose: () => {} })
	},
	StatusBarAlignment: { Right: 2 },
	TextEditorRevealType: { InCenter: 1 },
	SymbolKind: { Variable: 12, Function: 11, Class: 4, Method: 5 },
	ConfigurationTarget: { Global: 1 },
	ThemeColor: class { constructor(public id: string) {} },
	MarkdownString: class { 
		constructor(public value: string = "") { 
			this.isTrusted = false 
		}
		isTrusted = false
		appendMarkdown(value: string) { this.value += value; return this }
		appendCodeblock(value: string, language?: string) { 
			this.value += `\n\`\`\`${language || ''}\n${value}\n\`\`\`\n`
			return this 
		}
	},
	Hover: class { constructor(public contents: any, public range?: any) {} },
	Uri: {
		file: (path: string) => ({ fsPath: path, scheme: "file" })
	},
	Range: class {
		constructor(
			public start: { line: number; character: number },
			public end: { line: number; character: number }
		) {}
		
		get isEmpty() {
			return this.start.line === this.end.line && this.start.character === this.end.character
		}
		
		isEqual(other: any) {
			return this.start.line === other.start.line &&
				   this.start.character === other.start.character &&
				   this.end.line === other.end.line &&
				   this.end.character === other.end.character
		}
	},
	Position: class {
		constructor(public line: number, public character: number) {}
		
		isEqual(other: any) {
			return this.line === other.line && this.character === other.character
		}
	},
	Selection: class {
		constructor(
			public start: { line: number; character: number },
			public end: { line: number; character: number }
		) {}
	},
	Location: class {
		constructor(public uri: any, public range: any) {}
	}
}

// Apply mocks
const vscode = mockVscode as any
;(global as any).vscode = vscode

// Import types and services after mocking
import { 
	QaxChangeType, 
	DEFAULT_QAX_NEXT_EDIT_CONFIG,
	QaxJumpSuggestion 
} from "../../qaxNextEdit/types/QaxNextEditTypes"
import { NextEditType } from "../types/NextEditTypes"

// Test counter
let testCount = 0
let passedCount = 0
let failedCount = 0

function test(name: string, fn: () => void | Promise<void>): Promise<void> | void {
	testCount++
	console.log(`\n🧪 Integration Test ${testCount}: ${name}`)
	
	try {
		const result = fn()
		if (result instanceof Promise) {
			return result.then(() => {
				console.log(`✅ PASSED: ${name}`)
				passedCount++
			}).catch((error) => {
				console.log(`❌ FAILED: ${name}`)
				console.log(`   Error: ${error.message}`)
				failedCount++
			})
		} else {
			console.log(`✅ PASSED: ${name}`)
			passedCount++
		}
	} catch (error) {
		console.log(`❌ FAILED: ${name}`)
		console.log(`   Error: ${(error as Error).message}`)
		failedCount++
	}
}

async function runIntegrationTests() {
	console.log("🚀 Starting QaxNextEdit Integration Tests")
	console.log("=".repeat(50))

	// Test 1: Configuration detection
	test("Should detect QaxNextEdit configuration", () => {
		const config = vscodeTypes.workspace.getConfiguration("qax-code.nextEdit")
		const useQaxNextEdit = config.get("useQaxNextEdit", false)
		assert.strictEqual(useQaxNextEdit, true)
	})

	// Test 2: QaxJumpSuggestion to NextEditSuggestion conversion
	test("Should convert QaxJumpSuggestion to NextEditSuggestion format", () => {
		// Create mock QaxJumpSuggestion
		const qaxSuggestion: QaxJumpSuggestion = {
			id: "test-suggestion-1",
			filePath: "test.ts",
			range: new vscodeTypes.Range(0, 4, 0, 11),
			description: "Update variable name from 'oldName' to 'newName'",
			changeType: QaxChangeType.VARIABLE_RENAME,
			priority: 8,
			suggestedEdit: {
				range: new vscodeTypes.Range(0, 4, 0, 11),
				newText: "newName",
				description: "Rename 'oldName' to 'newName'"
			},
			relatedChange: {
				type: QaxChangeType.VARIABLE_RENAME,
				filePath: "test.ts",
				range: new vscodeTypes.Range(0, 4, 0, 11),
				oldValue: "oldName",
				newValue: "newName",
				confidence: 0.9
			}
		}

		// Mock NextEditService conversion method
		const convertSuggestion = (qaxSuggestion: QaxJumpSuggestion) => {
			let type: NextEditType = NextEditType.MODIFY
			let description = qaxSuggestion.description
			
			if (qaxSuggestion.relatedChange) {
				const change = qaxSuggestion.relatedChange
				if (change.type === "variable_rename" || change.type === "function_parameter_change") {
					type = NextEditType.MODIFY
					description = `Change: ${change.oldValue} ➜ ${change.newValue || "updated"}`
				} else if (change.type === "function_call_deletion" || change.type === "variable_deletion") {
					type = NextEditType.DELETE
					description = `Remove: ${change.oldValue}`
				}
			}

			const anchor = `line_${qaxSuggestion.range.start.line + 1}_char_${qaxSuggestion.range.start.character}`
			
			return {
				id: qaxSuggestion.id || `qax_suggestion_0`,
				type,
				description,
				location: {
					anchor,
					position: type === NextEditType.DELETE ? "replace" : "replace"
				},
				patch: {
					oldContent: "",
					newContent: qaxSuggestion.suggestedEdit?.newText || ""
				},
				reasoning: `QaxNextEdit detected ${qaxSuggestion.changeType} with ${Math.round((qaxSuggestion.relatedChange?.confidence || 0.8) * 100)}% confidence`,
				filePath: qaxSuggestion.filePath,
				createdAt: new Date()
			}
		}

		const converted = convertSuggestion(qaxSuggestion)

		// Verify conversion
		assert.strictEqual(converted.id, "test-suggestion-1")
		assert.strictEqual(converted.type, NextEditType.MODIFY)
		assert.strictEqual(converted.description, "Change: oldName ➜ newName")
		assert.strictEqual(converted.location.anchor, "line_1_char_4")
		assert.strictEqual(converted.location.position, "replace")
		assert.strictEqual(converted.patch.newContent, "newName")
		assert.ok(converted.reasoning.includes("90% confidence"))
		assert.strictEqual(converted.filePath, "test.ts")
	})

	// Test 3: Different change types conversion
	test("Should handle different change types correctly", () => {
		const testCases = [
			{
				changeType: QaxChangeType.VARIABLE_RENAME,
				oldValue: "oldVar",
				newValue: "newVar",
				expectedType: NextEditType.MODIFY,
				expectedDescription: "Change: oldVar ➜ newVar"
			},
			{
				changeType: QaxChangeType.FUNCTION_CALL_DELETION,
				oldValue: "deletedFunction()",
				newValue: undefined,
				expectedType: NextEditType.DELETE,
				expectedDescription: "Remove: deletedFunction()"
			},
			{
				changeType: QaxChangeType.VARIABLE_DELETION,
				oldValue: "deletedVar",
				newValue: undefined,
				expectedType: NextEditType.DELETE,
				expectedDescription: "Remove: deletedVar"
			}
		]

		testCases.forEach((testCase, index) => {
			const qaxSuggestion: QaxJumpSuggestion = {
				id: `test-${index}`,
				filePath: "test.ts",
				range: new vscodeTypes.Range(0, 0, 0, 10),
				description: "Test suggestion",
				changeType: testCase.changeType,
				priority: 5,
				relatedChange: {
					type: testCase.changeType,
					filePath: "test.ts",
					range: new vscodeTypes.Range(0, 0, 0, 10),
					oldValue: testCase.oldValue,
					newValue: testCase.newValue,
					confidence: 0.8
				}
			}

			// Mock conversion
			const convertSuggestion = (qaxSuggestion: QaxJumpSuggestion) => {
				let type: NextEditType = NextEditType.MODIFY
				let description = qaxSuggestion.description
				
				if (qaxSuggestion.relatedChange) {
					const change = qaxSuggestion.relatedChange
					if (change.type === "variable_rename" || change.type === "function_parameter_change") {
						type = NextEditType.MODIFY
						description = `Change: ${change.oldValue} ➜ ${change.newValue || "updated"}`
					} else if (change.type === "function_call_deletion" || change.type === "variable_deletion") {
						type = NextEditType.DELETE
						description = `Remove: ${change.oldValue}`
					}
				}

				return { type, description }
			}

			const converted = convertSuggestion(qaxSuggestion)
			assert.strictEqual(converted.type, testCase.expectedType, `Failed for ${testCase.changeType}`)
			assert.strictEqual(converted.description, testCase.expectedDescription, `Failed for ${testCase.changeType}`)
		})
	})

	// Test 4: Anchor generation
	test("Should generate appropriate anchors", () => {
		const testRanges = [
			{ line: 0, character: 0, expected: "line_1_char_0" },
			{ line: 5, character: 10, expected: "line_6_char_10" },
			{ line: 99, character: 50, expected: "line_100_char_50" }
		]

		testRanges.forEach(testRange => {
			const range = new vscodeTypes.Range(
				testRange.line, testRange.character,
				testRange.line, testRange.character + 10
			)

			const anchor = `line_${range.start.line + 1}_char_${range.start.character}`
			assert.strictEqual(anchor, testRange.expected)
		})
	})

	// Test 5: Complete workflow simulation
	test("Should handle complete workflow", async () => {
		// Simulate document with changes
		const document = await vscodeTypes.workspace.openTextDocument({
			content: "let newName = 5;\nconsole.log(newName);",
			language: "typescript"
		})

		assert.ok(document)
		assert.strictEqual(document.languageId, "typescript")

		// Simulate QaxNextEdit suggestions
		const mockSuggestions: QaxJumpSuggestion[] = [
			{
				id: "workflow-test-1",
				filePath: document.uri.fsPath,
				range: new vscodeTypes.Range(1, 12, 1, 19),
				description: "Update variable reference",
				changeType: QaxChangeType.VARIABLE_RENAME,
				priority: 9,
				suggestedEdit: {
					range: new vscodeTypes.Range(1, 12, 1, 19),
					newText: "newName",
					description: "Update variable reference"
				},
				relatedChange: {
					type: QaxChangeType.VARIABLE_RENAME,
					filePath: document.uri.fsPath,
					range: new vscodeTypes.Range(1, 12, 1, 19),
					oldValue: "oldName",
					newValue: "newName",
					confidence: 0.95
				}
			}
		]

		// Verify suggestions structure
		assert.strictEqual(mockSuggestions.length, 1)
		assert.strictEqual(mockSuggestions[0].changeType, QaxChangeType.VARIABLE_RENAME)
		assert.strictEqual(mockSuggestions[0].priority, 9)
		assert.ok(mockSuggestions[0].relatedChange)
		assert.strictEqual(mockSuggestions[0].relatedChange.confidence, 0.95)
	})

	// Wait for any async operations
	await new Promise(resolve => setTimeout(resolve, 100))

	// Print results
	console.log("\n" + "=".repeat(50))
	console.log("📊 QaxNextEdit Integration Test Results:")
	console.log(`  ✅ Passed: ${passedCount}`)
	console.log(`  ❌ Failed: ${failedCount}`)
	console.log(`  📈 Success Rate: ${Math.round((passedCount / testCount) * 100)}%`)

	if (failedCount === 0) {
		console.log("\n🎉 All integration tests passed!")
		console.log("🔗 QaxNextEdit integration is working correctly!")
		return true
	} else {
		console.log(`\n💥 ${failedCount} integration test(s) failed!`)
		return false
	}
}

// Run integration tests
runIntegrationTests().then((success) => {
	process.exit(success ? 0 : 1)
}).catch((error) => {
	console.error("💥 Error running integration tests:", error)
	process.exit(1)
})
